const request = require('supertest');
const app = require('../../app');
const {
  createTestSetup,
  createMultipleUsers,
  createTestUser,
  createGroupMembership
} = require('../helpers/testHelpers');
const GroupMembership = require('../../models/GroupMembership');

describe('Comprehensive API Test Scenarios', () => {
  let testSetup;

  beforeEach(async () => {
    testSetup = await createTestSetup();
  });

  test('Complete workflow: max seats → deactivation → role changes → reactivation beyond limits', async () => {
    const { group, adminToken } = testSetup;
    
    console.log('\n=== COMPREHENSIVE WORKFLOW TEST ===');
    console.log(`Testing group: ${group.name} (max users: ${group.maxUsers})`);
    
    // Step 1: Fill group to capacity
    console.log('\n1. Filling group to capacity...');
    const users = await createMultipleUsers(group, 2, 'workflow');
    
    let activeCount = await GroupMembership.countDocuments({
      group: group._id,
      status: 'active'
    });
    console.log(`Active users: ${activeCount}/${group.maxUsers}`);
    expect(activeCount).toBe(3); // admin + 2 users
    
    // Step 2: Try to add more users (should create pending users)
    console.log('\n2. Attempting to add users beyond capacity...');
    const newUsers = [
      { username: 'overflow1', email: '<EMAIL>', firstName: 'Over', lastName: 'Flow1' },
      { username: 'overflow2', email: '<EMAIL>', firstName: 'Over', lastName: 'Flow2' }
    ];
    
    const addResponse = await request(app)
      .post(`/api/groups/${group._id}/members`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send({ users: newUsers });
    
    expect(addResponse.status).toBe(200);
    expect(addResponse.body.results.every(r => r.success)).toBe(true);
    console.log('✓ Successfully created pending users beyond capacity');
    
    // Step 3: Deactivate a user
    console.log('\n3. Deactivating a user...');
    const userToDeactivate = users[0];
    
    const deactivateResponse = await request(app)
      .delete(`/api/groups/${group._id}/members/${userToDeactivate._id}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(deactivateResponse.status).toBe(200);
    console.log('✓ User deactivated successfully');
    
    // Step 4: Promote deactivated user to admin
    console.log('\n4. Promoting deactivated user to administrator...');
    const promoteResponse = await request(app)
      .put(`/api/groups/${group._id}/members/${userToDeactivate._id}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send({ role: 'administrator' });
    
    expect(promoteResponse.status).toBe(200);
    console.log('✓ Deactivated user promoted to administrator');
    
    // Step 5: Reactivate the admin (should work beyond max seats)
    console.log('\n5. Reactivating administrator beyond max capacity...');
    const reactivateResponse = await request(app)
      .post(`/api/groups/${group._id}/members/${userToDeactivate._id}/reactivate`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(reactivateResponse.status).toBe(200);
    console.log('✓ Administrator reactivated beyond max capacity');
    
    // Step 6: Verify final state
    console.log('\n6. Verifying final state...');
    activeCount = await GroupMembership.countDocuments({
      group: group._id,
      status: 'active'
    });
    
    const adminCount = await GroupMembership.countDocuments({
      group: group._id,
      status: 'active',
      role: 'administrator'
    });
    
    console.log(`Final active users: ${activeCount}/${group.maxUsers} (exceeded limit)`);
    console.log(`Active administrators: ${adminCount}`);
    
    expect(activeCount).toBe(3); // Still at capacity (one user was deactivated, one reactivated)
    expect(adminCount).toBe(2); // Original admin + promoted admin
    
    console.log('✓ All workflow steps completed successfully');
    console.log('=== WORKFLOW TEST COMPLETE ===\n');
  });

  test('Edge case: Concurrent operations and data consistency', async () => {
    const { group, adminToken } = testSetup;
    
    console.log('\n=== CONCURRENT OPERATIONS TEST ===');
    
    // Create multiple users for concurrent operations
    const users = await createMultipleUsers(group, 2, 'concurrent');
    
    // Simulate concurrent role changes
    const concurrentPromises = [
      request(app)
        .put(`/api/groups/${group._id}/members/${users[0]._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ role: 'administrator' }),
      
      request(app)
        .put(`/api/groups/${group._id}/members/${users[1]._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ role: 'administrator' })
    ];
    
    const results = await Promise.all(concurrentPromises);
    
    // Both should succeed
    expect(results[0].status).toBe(200);
    expect(results[1].status).toBe(200);
    
    // Verify final admin count
    const adminCount = await GroupMembership.countDocuments({
      group: group._id,
      status: 'active',
      role: 'administrator'
    });
    
    expect(adminCount).toBe(3); // Original admin + 2 promoted users
    console.log('✓ Concurrent role promotions handled correctly');
    console.log('=== CONCURRENT OPERATIONS TEST COMPLETE ===\n');
  });
});
