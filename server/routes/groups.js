const express = require('express');
const router = express.Router();
const Group = require('../models/Group');
const User = require('../models/User');
const GroupMembership = require('../models/GroupMembership');
const auth = require('../middleware/auth');

// Create a new group (during registration)
router.post('/', async (req, res) => {
  try {
    const { name, code, tier, createdBy } = req.body;

    // Validate tier
    const validTiers = ['1-3', '4-25', '26-100', '100+'];
    if (!validTiers.includes(tier)) {
      return res.status(400).json({ message: 'Invalid tier selected' });
    }

    // Check if code is available
    const existingGroup = await Group.findOne({ code });
    if (existingGroup) {
      return res.status(400).json({ message: 'Group code already exists' });
    }

    // Create the group
    const group = new Group({
      name,
      code,
      tier,
      createdBy
    });

    await group.save();

    res.status(201).json(group);
  } catch (error) {
    console.error('Error creating group:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get group details
router.get('/:id', auth, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id)
      .populate('createdBy', 'username email firstName lastName');

    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user belongs to this group or is super user
    const user = await User.findById(req.user.userId);
    if (!user.isSuperUser && user.group.toString() !== group._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(group);
  } catch (error) {
    console.error('Error fetching group:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update group (welcome message, tier change)
router.put('/:id', auth, async (req, res) => {
  try {
    const { welcomeMessage, tier } = req.body;
    const group = await Group.findById(req.params.id);

    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is admin of this group
    const membership = await GroupMembership.findOne({
      group: group._id,
      user: req.user.userId,
      role: 'administrator',
      status: 'active'
    });

    if (!membership) {
      return res.status(403).json({ message: 'Only administrators can update group settings' });
    }

    // If changing tier, check current user count
    if (tier && tier !== group.tier) {
      const currentActiveUsers = await GroupMembership.getActiveMembersCount(group._id);
      const newMaxUsers = Group.getMaxUsersForTier(tier);
      
      if (currentActiveUsers > newMaxUsers) {
        return res.status(400).json({ 
          message: `Cannot downgrade to ${tier} tier. You have ${currentActiveUsers} active users but the new tier allows only ${newMaxUsers}. Please deactivate ${currentActiveUsers - newMaxUsers} users first.`,
          currentUsers: currentActiveUsers,
          newLimit: newMaxUsers
        });
      }
      
      group.tier = tier;
    }

    if (welcomeMessage !== undefined) {
      group.welcomeMessage = welcomeMessage;
    }

    await group.save();
    res.json(group);
  } catch (error) {
    console.error('Error updating group:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get group members
router.get('/:id/members', auth, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id);
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user belongs to this group or is super user
    const user = await User.findById(req.user.userId);
    if (!user.isSuperUser && user.group.toString() !== group._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const members = await GroupMembership.getGroupMembersWithUsers(group._id);
    
    res.json({
      members,
      totalMembers: members.length,
      activeMembers: members.filter(m => m.status === 'active').length,
      maxUsers: group.maxUsers,
      availableSeats: group.maxUsers - members.filter(m => m.status === 'active').length
    });
  } catch (error) {
    console.error('Error fetching group members:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Add members to group (admin functionality)
router.post('/:id/members', auth, async (req, res) => {
  try {
    const { users } = req.body; // Array of { email, firstName, lastName, username }
    const group = await Group.findById(req.params.id);

    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is admin of this group
    const membership = await GroupMembership.findOne({
      group: group._id,
      user: req.user.userId,
      role: 'administrator',
      status: 'active'
    });

    if (!membership) {
      return res.status(403).json({ message: 'Only administrators can add members' });
    }

    // Note: Admins can add unlimited users to manage pending users
    // Seat limits are enforced during user activation/registration

    const results = [];
    
    for (const userData of users) {
      try {
        // Check if username is already taken in this group
        const existingUser = await User.findOne({ 
          username: userData.username, 
          group: group._id 
        });
        
        if (existingUser) {
          results.push({
            username: userData.username,
            success: false,
            error: 'Username already exists in this group'
          });
          continue;
        }

        // Create user in pending state
        const user = new User({
          username: userData.username,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          group: group._id,
          groupStatus: 'pending',
          password: 'temp_password_to_be_set' // Will be set during registration
        });

        await user.save();

        // Create group membership
        const groupMembership = new GroupMembership({
          group: group._id,
          user: user._id,
          role: 'member',
          status: 'pending',
          invitedBy: req.user.userId
        });

        await groupMembership.save();

        results.push({
          username: userData.username,
          success: true,
          userId: user._id
        });

        // TODO: Send invitation email here (stubbed for now)
        console.log(`Would send invitation email to ${userData.email} for group ${group.name}`);

      } catch (error) {
        results.push({
          username: userData.username,
          success: false,
          error: error.message
        });
      }
    }

    res.json({ results });
  } catch (error) {
    console.error('Error adding group members:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Remove member from group
router.delete('/:id/members/:userId', auth, async (req, res) => {
  try {
    console.log('=== DEBUG: Remove member request ===');
    console.log('Group ID:', req.params.id);
    console.log('User ID to remove:', req.params.userId);
    console.log('Requesting user ID:', req.user.userId);

    const group = await Group.findById(req.params.id);
    if (!group) {
      console.log('ERROR: Group not found');
      return res.status(404).json({ message: 'Group not found' });
    }
    console.log('Group found:', group.name);

    // Get the user being removed
    const userToRemove = await User.findById(req.params.userId);
    if (!userToRemove) {
      console.log('ERROR: User to remove not found');

      // Let's check if there's a GroupMembership record for this user
      const orphanedMembership = await GroupMembership.findOne({
        group: group._id,
        user: req.params.userId
      });

      if (orphanedMembership) {
        console.log('Found orphaned membership record:', {
          id: orphanedMembership._id,
          role: orphanedMembership.role,
          status: orphanedMembership.status,
          user: orphanedMembership.user
        });

        // Clean up the orphaned membership record
        console.log('Removing orphaned membership record...');
        await GroupMembership.deleteOne({ _id: orphanedMembership._id });
        console.log('Orphaned membership record removed successfully');

        return res.json({ message: 'Orphaned user record cleaned up successfully' });
      }

      return res.status(404).json({ message: 'User not found' });
    }
    console.log('User to remove found:', userToRemove.username);

    // Prevent super users from deleting themselves
    if (req.user.userId === req.params.userId && userToRemove.isSuperUser) {
      console.log('ERROR: Super user trying to remove themselves');
      return res.status(403).json({
        message: 'Super users cannot remove themselves from groups'
      });
    }

    // Check if user is admin of this group
    const adminMembership = await GroupMembership.findOne({
      group: group._id,
      user: req.user.userId,
      role: 'administrator',
      status: 'active'
    });

    if (!adminMembership) {
      console.log('ERROR: Requesting user is not an admin');
      return res.status(403).json({ message: 'Only administrators can remove members' });
    }
    console.log('Admin membership verified');

    // Find the membership to remove
    console.log('Looking for membership with group:', group._id, 'user:', req.params.userId);
    const membership = await GroupMembership.findOne({
      group: group._id,
      user: req.params.userId
    });

    console.log('Membership found:', membership ? 'YES' : 'NO');
    if (membership) {
      console.log('Membership details:', {
        id: membership._id,
        role: membership.role,
        status: membership.status,
        user: membership.user
      });
    }

    // Let's also check all memberships for this group to debug
    const allMemberships = await GroupMembership.find({ group: group._id });
    console.log('All memberships for this group:');
    allMemberships.forEach(m => {
      console.log(`- User: ${m.user}, Role: ${m.role}, Status: ${m.status}`);
    });

    if (!membership) {
      console.log('ERROR: Membership not found');
      return res.status(404).json({ message: 'User not found in group' });
    }

    // Check if removing this user would leave no administrators
    if (membership.role === 'administrator') {
      const adminCount = await GroupMembership.countDocuments({
        group: group._id,
        role: 'administrator',
        status: 'active'
      });

      if (adminCount <= 1) {
        return res.status(400).json({
          message: 'Cannot remove the last administrator from the group. Promote another user to administrator first.'
        });
      }
    }

    // Additional check: prevent removing yourself if you're the only admin
    if (req.user.userId === req.params.userId && membership.role === 'administrator') {
      const otherAdminCount = await GroupMembership.countDocuments({
        group: group._id,
        role: 'administrator',
        status: 'active',
        user: { $ne: req.params.userId }
      });

      if (otherAdminCount === 0) {
        return res.status(400).json({
          message: 'You cannot remove yourself as you are the only administrator. Promote another user to administrator first.'
        });
      }
    }

    // Deactivate the membership and user
    await membership.deactivate();

    if (userToRemove) {
      await userToRemove.deactivate();
    }

    res.json({ message: 'User removed from group successfully' });
  } catch (error) {
    console.error('Error removing group member:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update member role (promote to admin)
router.put('/:id/members/:userId', auth, async (req, res) => {
  try {
    const { role } = req.body;
    const group = await Group.findById(req.params.id);

    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is admin of this group
    const adminMembership = await GroupMembership.findOne({
      group: group._id,
      user: req.user.userId,
      role: 'administrator',
      status: 'active'
    });

    if (!adminMembership) {
      return res.status(403).json({ message: 'Only administrators can change member roles' });
    }

    // Find the membership to update
    const membership = await GroupMembership.findOne({
      group: group._id,
      user: req.params.userId
    });

    if (!membership) {
      return res.status(404).json({ message: 'User not found in group' });
    }

    membership.role = role;
    await membership.save();

    res.json({ message: 'Member role updated successfully' });
  } catch (error) {
    console.error('Error updating member role:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Generate group code suggestion
router.post('/suggest-code', async (req, res) => {
  try {
    const { name } = req.body;
    
    if (!name) {
      return res.status(400).json({ message: 'Group name is required' });
    }

    let suggestedCode = Group.generateCodeFromName(name);
    
    // Check if the suggested code is available
    let isAvailable = await Group.isCodeAvailable(suggestedCode);
    let counter = 1;
    
    // If not available, try adding numbers
    while (!isAvailable && counter <= 99) {
      const numberedCode = `${suggestedCode}-${counter}`;
      isAvailable = await Group.isCodeAvailable(numberedCode);
      if (isAvailable) {
        suggestedCode = numberedCode;
        break;
      }
      counter++;
    }

    res.json({ 
      suggestedCode,
      isAvailable 
    });
  } catch (error) {
    console.error('Error generating code suggestion:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Validate group code (for registration flow)
router.post('/validate-code', async (req, res) => {
  try {
    const { groupCode } = req.body;

    if (!groupCode) {
      return res.status(400).json({ message: 'Group code is required' });
    }

    // Find the group
    const group = await Group.findOne({ code: groupCode, status: 'active' });
    if (!group) {
      return res.status(400).json({ message: 'Group not found. Please check the group code or contact your administrator.' });
    }

    // Check if group has reached its active user limit
    const activeUsersCount = await GroupMembership.countDocuments({
      group: group._id,
      status: 'active'
    });

    if (activeUsersCount >= group.maxUsers) {
      return res.status(400).json({
        message: 'This group has reached its maximum number of active members. Please contact your administrator.'
      });
    }

    // Group is valid and has available seats
    res.json({
      valid: true,
      groupName: group.name,
      availableSeats: group.maxUsers - activeUsersCount
    });
  } catch (error) {
    console.error('Error validating group code:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
